import ssl

ssl._create_default_https_context = ssl._create_unverified_context
from pyVmomi import vim
from pyvim.connect import SmartConnect, Disconnect

from pyVmomi import vmodl

servers = [
    ('*************', '16613', 'Tt9566659', 443),
    ('*************', '<EMAIL>', 'AD)&)VC%%', 443),
    ('*************', '<EMAIL>', '`1qaz~!QAZ', 443),
]

for server, username, password, prot in servers:
    # 建立到 vSphere 服务器的连接
    si = SmartConnect(host=server, user=username, pwd=password, port=prot)

    # 获取 vSphere 中的所有数据中心
    content = si.RetrieveContent()
    root_folder = content.rootFolder

    # 获取第一个数据中心
    vm_datacenter = root_folder.childEntity[0]
    print("Datacenter name: {}".format(vm_datacenter.name))
    print("Datacenter status: {}".format(vm_datacenter.overallStatus))

    # 获取数据中心下的所有主机
    host_folder = vm_datacenter.hostFolder
    hosts = host_folder.childEntity
    for host in hosts:
        print("Host name: {}".format(host.name))
        print("Host status: {}".format(host.summary.overallStatus))
        # print("Host CPU usage: {} MHz".format(host.summary.quickStats.overallCpuUsage))
        # print("Host memory usage: {} MB".format(host.summary.quickStats.overallMemoryUsage))

    # 获取数据中心下的所有存储
    storage_folder = vm_datacenter.datastoreFolder
    datastores = storage_folder.childEntity
    for datastore in datastores:
        summary = datastore.summary
        print("Datastore name: {}".format(summary.name))
        print("Datastore type: {}".format(summary.type))
        print("Datastore capacity: {} GB".format(summary.capacity / 1024 / 1024 / 1024))
        print("Datastore free space: {} GB".format(summary.freeSpace / 1024 / 1024 / 1024))

    # 获取数据中心下的所有虚拟机
    vm_folder = vm_datacenter.vmFolder
    vms = vm_folder.childEntity
    for vm in vms:
        print("Virtual machine name: {}".format(vm.name))
        # print("Virtual machine status: {}".format(vm.summary.overallStatus))
        print("Virtual machine CPU usage: {} MHz".format(vm.summary.quickStats.overallCpuUsage))
        print("Virtual machine memory usage: {} MB".format(vm.summary.quickStats.guestMemoryUsage))

    # 断开与 vCenter 的连接
    Disconnect(si)
